{"name": "plugin-snapshot-box", "description": "ElizaOS plugin for snapshot-box", "version": "0.1.0", "type": "module", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "packageType": "plugin", "platform": "node", "license": "UNLICENSED", "author": "${GITHUB_USERNAME}", "keywords": ["plugin", "<PERSON><PERSON><PERSON>"], "repository": {"type": "git", "url": ""}, "homepage": "https://elizaos.ai", "bugs": {"url": "https://github.com/${GITHUB_USERNAME}/${PLUGINNAME}/issues"}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}}, "files": ["dist", "README.md", ".n<PERSON><PERSON><PERSON>", "package.json", "tsup.config.ts"], "dependencies": {"@elizaos/core": "latest", "@tanstack/react-query": "^5.80.7", "clsx": "^2.1.1", "tailwindcss": "^4.1.10", "tailwind-merge": "^3.3.1", "vite": "^6.3.5", "zod": "3.24.2"}, "peerDependencies": {}, "devDependencies": {"@elizaos/cli": "latest", "@tailwindcss/vite": "^4.1.10", "@vitejs/plugin-react-swc": "^3.10.2", "dotenv": "16.4.5", "prettier": "3.5.3", "tailwindcss-animate": "^1.0.7", "tsup": "8.5.0", "typescript": "5.8.2"}, "scripts": {"start": "el<PERSON><PERSON> start", "dev": "el<PERSON><PERSON> dev", "build": "tsc --noEmit && vite build && tsup", "lint": "prettier --write ./src", "test:component": "bun run test:install && bun test", "test:e2e": "bun run test:install && bun test", "test:e2e:manual": "bun run test:install && node scripts/test-e2e-manual.js", "test:cypress": "bun run test:install && cypress run --component", "test": "bun run test:install && bun run test:component && bun run test:e2e", "test:install": "node scripts/install-test-deps.js", "publish": "elizaos publish", "format": "prettier --write ./src", "format:check": "prettier --check ./src"}, "publishConfig": {"access": "public"}, "resolutions": {"zod": "3.24.2"}, "agentConfig": {"pluginType": "elizaos:plugin:1.0.0", "pluginParameters": {"API_KEY": {"type": "string", "description": "API key for the service"}}}, "gitHead": "d5bd5c43bfebeb7ac02f9e029f924cb6cd5c2ec7"}