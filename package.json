{"name": "my-agent", "description": "Project starter for eliza<PERSON>", "version": "0.1.0", "type": "module", "main": "dist/src/index.js", "module": "dist/src/index.js", "types": "dist/index.d.ts", "keywords": ["project", "<PERSON><PERSON><PERSON>"], "repository": {"type": "git", "url": ""}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/src/index.js"}}}, "files": ["dist"], "dependencies": {"@elizaos/cli": "latest", "@elizaos/core": "latest", "@elizaos/plugin-bootstrap": "latest", "@elizaos/plugin-evm": "1.0.12", "@elizaos/plugin-google-genai": "1.0.0", "@elizaos/plugin-sql": "latest", "@tanstack/react-query": "^5.29.0", "axios": "^1.10.0", "clsx": "^2.1.1", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwind-merge": "^2.6.0", "tailwindcss": "^4.1.10", "zod": "3.24.2"}, "devDependencies": {"@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "prettier": "3.5.3", "tsup": "8.5.0", "typescript": "^5.6.3", "vite": "^6.0.1"}, "scripts": {"start": "el<PERSON><PERSON> start", "dev": "el<PERSON><PERSON> dev", "build": "tsc --noEmit && vite build && tsup", "lint": "prettier --write ./src", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch", "test:component": "bun run test:install && bun test", "test:e2e": "bun run test:install && bun test", "test": "bun run test:install && bun run test:component && bun run test:e2e", "test:coverage": "bun run test:install && bun test --coverage", "test:watch": "bun run test:install && bun test --watch", "test:install": "node scripts/install-test-deps.js", "format": "prettier --write ./src", "format:check": "prettier --check ./src", "check-all": "bun run type-check && bun run format:check && bun run test", "cy:open": "bun run test:install && cypress open", "cy:run": "bun run test:install && cypress run --component", "cy:test": "bun run test:install && cypress run --component --reporter spec", "cypress:component": "bun run test:install && cypress run --component", "cypress:e2e": "bun run test:install && cypress run --e2e", "cypress:open": "bun run test:install && cypress open"}, "publishConfig": {"access": "public"}, "gitHead": "b165ad83e5f7a21bc1edbd83374ca087e3cd6b33"}